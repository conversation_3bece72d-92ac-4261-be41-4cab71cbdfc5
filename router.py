"""Router for integration scrapers e.g., scheduling."""

import os
import json
import os.path
import tempfile
from typing import Optional

from fastapi import (
    APIRouter,
    HTTPException,
    Form,
    File,
    Request,
    status,
    UploadFile,
)
from fastapi.responses import JSONResponse
from pyzerox import zerox

from handlers import handle_request
from integrations.zerox.retry import retry_with_backoff
from utils.json import parse_request
from utils.logging import logger


router = APIRouter()


@router.post("/")
async def scraper_endpoint(request: Request):
    """Handle scraper requests."""
    try:
        input_data = await request.json()
        if not input_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing or invalid JSON body",
            )

        req = parse_request(input_data)
        response = handle_request(req)
        if not response.success:
            if "not implemented" in response.message.lower():
                status_code = status.HTTP_501_NOT_IMPLEMENTED
            else:
                status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            return JSONResponse(
                status_code=status_code, content=response.model_dump()
            )

        return JSONResponse(
            status_code=status.HTTP_200_OK, content=response.model_dump()
        )

    except Exception as e:
        logger.error(f"Error processing request: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "success": False,
                "message": f"Error processing request: {str(e)}",
                "errors": [str(e)],
            },
        )


@router.post("/zerox/markdown")
async def process_document(
    file: UploadFile = File(...),
    model: str = Form("gpt-4o-mini"),
    custom_prompt: Optional[str] = Form(None),
    num_pages: Optional[int] = Form(None),
):
    """Process a document using zerox."""
    temp_file_path = None
    fd = None

    try:
        # Create a secure temporary file with a random name - zerox automatically cleans this up
        fd, temp_file_path = tempfile.mkstemp(
            suffix=os.path.splitext(file.filename)[1]
        )

        # Immediately wrap the file descriptor to ensure it gets closed
        with os.fdopen(fd, "wb") as temp_file:
            fd = None  # Mark as handled to avoid double-close
            content = await file.read()
            temp_file.write(content)

        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="OPENAI_API_KEY environment variable is not set",
            )

        os.environ["OPENAI_API_KEY"] = openai_api_key

        request = {
            "file_path": temp_file_path,
            "model": model,
            "custom_system_prompt": custom_prompt,
        }

        if num_pages is not None:
            request["select_pages"] = num_pages

        async def call_zerox():
            # Don't let zerox clean up the file after processing.
            # We now have zerox retry logic so if an error occurs,
            # we do not want zerox to automatically delete the file
            # before we can retry. Note that we do handle file cleanup
            # in the finally block.
            return await zerox(**request, cleanup=False)

        result = await retry_with_backoff(call_zerox)

        if not result:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to process document - no result returned from zerox",
            )

        response = {
            "completion_time": result.completion_time,
            "file_name": result.file_name,
            "input_tokens": result.input_tokens,
            "output_tokens": result.output_tokens,
            "pages": [
                {
                    "content": page.content,
                    "content_length": page.content_length,
                    "page": page.page,
                }
                for page in result.pages
            ],
        }

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": "Document processed successfully",
                "result": response,
            },
        )

    except Exception as e:
        logger.error(f"Error processing document: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "success": False,
                "message": f"Error processing document: {str(e)}",
                "errors": [str(e)],
            },
        )
    finally:
        # Ensure file descriptor is closed if it wasn't handled by os.fdopen
        if fd is not None:
            try:
                os.close(fd)
            except OSError:
                pass  # Already closed

        # Ensure temporary file is deleted
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
            except OSError as e:
                logger.warning(
                    f"Failed to remove temporary file {json.dumps({'temp_file_path': temp_file_path})}: {e}"
                )


@router.get("/health")
async def health_check():
    """Health check endpoint."""
    return JSONResponse(
        status_code=status.HTTP_200_OK, content={"status": "healthy"}
    )
