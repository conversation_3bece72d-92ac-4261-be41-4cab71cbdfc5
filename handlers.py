"""Dynamic handler for scheduling scrapers."""

import importlib
import json
from typing import Callable, Dict, Optional, Tuple, Union

from models.base import BaseRequest, BaseResponse, RequestMode
from utils.logging import logger

from integrations.scheduling.models import (
    GetWarehouseRequest,
    GetOpenSlotsRequest,
    GetLoadTypesRequest,
    GetAppointmentRequest,
    MakeAppointmentRequest,
    UpdateAppointmentRequest,
    CancelAppointmentRequest,
    GetWarehouseResponse,
    GetOpenSlotsResponse,
    GetLoadTypesResponse,
    GetAppointmentResponse,
    MakeAppointmentResponse,
    UpdateAppointmentResponse,
    CancelAppointmentResponse,
)


AnyRequest = Union[
    GetWarehouseRequest,
    GetOpenSlotsRequest,
    GetLoadTypesRequest,
    GetAppointmentRequest,
    MakeAppointmentRequest,
    UpdateAppointmentRequest,
    CancelAppointmentRequest,
    BaseRequest,
]

AnyResponse = Union[
    GetWarehouseResponse,
    GetOpenSlotsResponse,
    GetLoadTypesResponse,
    GetAppointmentResponse,
    MakeAppointmentResponse,
    UpdateAppointmentResponse,
    CancelAppointmentResponse,
    BaseResponse,
]

_HANDLERS: Dict[str, Tuple[Dict[str, Callable], Dict[str, Callable]]] = {}


def _load_integration(integration: str, platform: str) -> bool:
    """Dynamically load an integration module."""
    try:
        module_path = f"integrations.{integration}.{platform.lower()}.handlers"
        module = importlib.import_module(module_path)

        if hasattr(module, "SELENIUM_HANDLERS") and hasattr(
            module, "API_HANDLERS"
        ):
            cache_key = f"{integration}.{platform.lower()}"
            _HANDLERS[cache_key] = (
                module.SELENIUM_HANDLERS,
                module.API_HANDLERS,
            )
            logger.info(
                f"Loaded handlers for {json.dumps({'integration': integration, 'platform': platform.lower()})}"
            )
            return True
        else:
            logger.error(
                f"No SELENIUM_HANDLERS or API_HANDLERS defined in {json.dumps({'module_path': module_path})}"
            )
            return False
    except ImportError as e:
        logger.error(f"Failed to import module: {str(e)}")
        return False


def _get_handler(
    integration: str,
    platform: str,
    action: str,
    mode: str = RequestMode.SELENIUM,
) -> Optional[Callable[[AnyRequest], AnyResponse]]:
    """Get the handler function for the specified integration, platform and action."""
    cache_key = f"{integration}.{platform.lower()}"

    if cache_key not in _HANDLERS and not _load_integration(
        integration, platform
    ):
        return None

    selenium_handlers, api_handlers = _HANDLERS.get(cache_key, ({}, {}))
    handlers = api_handlers if mode == RequestMode.API else selenium_handlers
    return handlers.get(action)


def handle_request(request: AnyRequest) -> AnyResponse:
    """Process a request by routing it to the appropriate handler."""

    integration = (
        request.integration.value
        if hasattr(request.integration, "value")
        else request.integration
    )
    platform = (
        request.platform.value
        if hasattr(request.platform, "value")
        else request.platform
    )
    action = (
        request.action.value
        if hasattr(request.action, "value")
        else request.action
    )
    mode = (
        request.mode.value if hasattr(request.mode, "value") else request.mode
    )

    logger.info(
        f"Processing request: {json.dumps({'integration': integration, 'platform': platform, 'action': action, 'mode': mode})}"
    )

    handler = _get_handler(integration, platform, action, mode)

    if not handler:
        logger.error(
            f"No handler found for {json.dumps({'integration': integration, 'platform': platform, 'action': action, 'mode': mode})}"
        )
        return BaseResponse(
            success=False,
            message=f"Unsupported operation: {integration}/{platform}/{action} with mode {mode}",
        )

    try:
        logger.info(
            f"Executing {mode} handler for {json.dumps({'integration': integration, 'platform': platform, 'action': action, 'mode': mode})}"
        )
        return handler(request)
    except Exception as e:
        logger.error(f"Error handling request: {str(e)}")
        return BaseResponse(
            success=False,
            message=f"Error handling request: {str(e)}",
            errors=[str(e)],
        )
