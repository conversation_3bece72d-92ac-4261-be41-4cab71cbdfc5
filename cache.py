"""Cookie cache functions using Redis."""

import json
import pickle
from typing import Any, List, Optional

import redis
from redis.exceptions import RedisError

from config import REDIS_HOST, REDIS_PORT, REDIS_PASSWORD, REDIS_PREFIX
from utils.logging import logger

redis_client = redis.Redis(
    host=REDIS_HOST,
    port=REDIS_PORT,
    password=REDIS_PASSWORD,
    decode_responses=False,
    socket_timeout=5,
    socket_connect_timeout=5,
    retry_on_timeout=True,
)


def get_cache_key(platform: str, user_id: str) -> str:
    """Generate a cache key for the given platform and user ID.

    Args:
        platform: The platform identifier
        user_id: The user identifier

    Returns:
        The cache key
    """
    return f"{REDIS_PREFIX}:cookies:{platform}:{user_id}"


def save_cookies(platform: str, user_id: str, cookies: List[dict]) -> bool:
    """Save cookies to Redis cache.

    Args:
        platform: The platform identifier
        user_id: The user identifier
        cookies: The cookies to cache

    Returns:
        True if successful, False otherwise
    """
    cache_key = get_cache_key(platform, user_id)

    try:
        pickled_cookies = pickle.dumps(cookies)

        # Store in Redis with a 24-hour TTL by default
        redis_client.setex(cache_key, 86400, pickled_cookies)

        logger.info(
            f"Saved cookies for {json.dumps({'platform': platform, 'user_id': user_id})} to Redis"
        )
        return True
    except (RedisError, pickle.PickleError) as e:
        logger.error(f"Failed to save cookies to Redis: {str(e)}")
        return False


def load_cookies(platform: str, user_id: str) -> Optional[List[dict]]:
    """Load cookies from Redis cache.

    Args:
        platform: The platform identifier
        user_id: The user identifier

    Returns:
        The cached cookies if found, None otherwise
    """
    cache_key = get_cache_key(platform, user_id)

    try:
        pickled_cookies = redis_client.get(cache_key)

        if not pickled_cookies:
            logger.info(
                f"No cached cookies found for {json.dumps({'platform': platform, 'user_id': user_id})}"
            )
            return None

        cookies = pickle.loads(pickled_cookies)

        logger.info(
            f"Loaded cookies from Redis for {json.dumps({'platform': platform, 'user_id': user_id})}"
        )
        return cookies
    except (RedisError, pickle.PickleError) as e:
        logger.warning(f"Failed to load cookies from Redis: {str(e)}")
        return None


def clear_cookies(platform: str, user_id: str) -> bool:
    """Clear cached cookies from Redis.

    Args:
        platform: The platform identifier
        user_id: The user identifier

    Returns:
        True if successful, False otherwise
    """
    cache_key = get_cache_key(platform, user_id)

    try:
        redis_client.delete(cache_key)

        logger.info(
            f"Cleared cookies for {json.dumps({'platform': platform, 'user_id': user_id})}"
        )
        return True
    except RedisError as e:
        logger.error(f"Failed to clear cookies from Redis: {str(e)}")
        return False


def get_or_create_cache(cache_key: str, creator_func, ttl: int = 3600) -> Any:
    """Generic method to get or create a cached value with Redis.

    Args:
        cache_key: The cache key
        creator_func: Function to create the value if not in cache
        ttl: Time-to-live in seconds (default: 1 hour)

    Returns:
        The cached or newly created value
    """
    try:
        cached_data = redis_client.get(cache_key)

        if cached_data:
            return pickle.loads(cached_data)

        value = creator_func()

        redis_client.setex(cache_key, ttl, pickle.dumps(value))

        return value
    except (RedisError, pickle.PickleError) as e:
        logger.warning(f"Cache operation failed: {str(e)}")
        return creator_func()
