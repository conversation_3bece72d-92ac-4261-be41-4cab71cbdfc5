"""E2open utilities."""

from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Optional
import re
import time
import json

from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriver<PERSON>ait

from utils.logging import logger
from utils.selenium import (
    safe_click,
    safe_send_keys,
    wait_for_page_load,
)

from integrations.scheduling.models import (
    AppointmentSlot,
    WarehouseDetails,
)

from integrations.scheduling.e2open.models import (
    E2openAppointmentData,
    E2openAppointmentRequestValidator,
    E2openAppointmentValidationResult,
    E2openGetOpenSlotsRequest,
    E2openMatchingRow,
    E2openMatchingRowsResult,
    E2openOpenSlotsValidationResult,
    E2openWarehouseValidator,
)

from selenium.webdriver.support.ui import Select


# E2open element locators
LOCATORS = {
    # Login indicators
    "username_field": (By.ID, "userID"),
    "password_field": (By.ID, "password"),
    "login_button": (By.ID, "userSubmit"),
    "remember_me": (By.ID, "rememberMe"),
    "user_login_tab": (By.CSS_SELECTOR, "[data-tab-id='userLogin']"),
    "login_error": (By.CSS_SELECTOR, ".footer.msg.text-block-5"),
    "logged_in_indicator": (By.ID, "headerUserItem"),
    "cookie_button_locator": (By.ID, "accept-button"),
    # Base URLs and endpoints
    "base_url": "https://na-app.tms.e2open.com",
    "appointment_search_endpoint": "/apptschedule/carrierappointmentsearch.do",
    "3rd_party_appointment": "/thirdparty/selecttpuseracct.do",
    "pro_id_input": (By.NAME, "proNumString"),
    "search_button": (By.ID, "searchButton"),
}


COMPANY_WISE_LOCATORS = {
    "dart container": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[2].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[2].searchString",
        ),
        "3rd_party_pickup_zip_code_input": (By.NAME, "pickGeo"),
        "3rd_party_dropoff_zip_code_input": None,
    },
    "abbott nutrition": {
        "pickup_toggel": None,
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": None,
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[0].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    "america's logistics, llc": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": None,
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[0].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": None,
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    "american sugar refining, inc": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[1].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[1].searchString",
        ),
        "3rd_party_pickup_zip_code_input": (By.NAME, "pickGeo"),
        "3rd_party_dropoff_zip_code_input": (By.NAME, "dropGeo"),
    },
    "associated wholesale grocers": {
        "pickup_toggel": None,
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": None,
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[0].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": (By.NAME, "dropGeo"),
    },
    "batory foods": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[0].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[0].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    "bay valley foods": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[0].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[0].searchString",
        ),
        "3rd_party_pickup_zip_code_input": (By.NAME, "pickGeo"),
        "3rd_party_dropoff_zip_code_input": (By.NAME, "dropGeo"),
    },
    "coca-cola beverages florida": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[0].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[0].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    "coca-cola united": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[0].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[0].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    "crown cork and seal": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[0].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[0].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    "ferrara candy": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[1].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[1].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    "flagstone foods llc": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": None,
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[0].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": None,
        "3rd_party_pickup_zip_code_input": (By.NAME, "pickGeo"),
        "3rd_party_dropoff_zip_code_input": None,
    },
    "heartland coca-cola": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[0].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[0].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    "heartland foods": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[0].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[0].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": (By.NAME, "dropGeo"),
    },
    "hellofresh": {
        "pickup_toggel": None,
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": None,
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[0].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    "high liner foods inc": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[0].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[0].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    "ingredion": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[0].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[0].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    "ken's foods": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": None,
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[2].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": None,
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    "lassonde industries inc": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[2].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[2].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    "mount franklin foods": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[1].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[1].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    "phillips pet": {
        "pickup_toggel": None,
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": None,
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[0].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    "reyes logistics solutions, llc": {
        "glccb/rccb scheduling": {
            "pickup_toggel": (
                By.XPATH,
                "//input[@name='searchType'][@value='1000']",
            ),
            "dropoff_toggel": (
                By.XPATH,
                "//input[@name='searchType'][@value='1001']",
            ),
            "3rd_party_pickup_pro_id_input": (
                By.NAME,
                "pickRefNums[1].searchString",
            ),
            "3rd_party_dropoff_pro_id_input": (
                By.NAME,
                "dropRefNums[2].searchString",
            ),
            "3rd_party_pickup_zip_code_input": None,
            "3rd_party_dropoff_zip_code_input": None,
        },
        "reyes beer division": {
            "pickup_toggel": None,
            "dropoff_toggel": (
                By.XPATH,
                "//input[@name='searchType'][@value='1001']",
            ),
            "3rd_party_pickup_pro_id_input": None,
            "3rd_party_dropoff_pro_id_input": (
                By.NAME,
                "dropRefNums[0].searchString",
            ),
            "3rd_party_pickup_zip_code_input": None,
            "3rd_party_dropoff_zip_code_input": None,
        },
    },
    "swire coca-cola": {
        "pickup_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1000']",
        ),
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": (
            By.NAME,
            "pickRefNums[1].searchString",
        ),
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[1].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    "toys r us": {
        "pickup_toggel": None,
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": None,
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[0].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": (By.NAME, "dropGeo"),
    },
    "ulta beauty": {
        "pickup_toggel": None,
        "dropoff_toggel": (
            By.XPATH,
            "//input[@name='searchType'][@value='1001']",
        ),
        "3rd_party_pickup_pro_id_input": None,
        "3rd_party_dropoff_pro_id_input": (
            By.NAME,
            "dropRefNums[0].searchString",
        ),
        "3rd_party_pickup_zip_code_input": None,
        "3rd_party_dropoff_zip_code_input": None,
    },
    # TODO NEed to update it
    "winland foods": {
        "winland foods 3rd party appt sched": {
            "pickup_toggel": (
                By.XPATH,
                "//input[@name='searchType'][@value='1000']",
            ),
            "dropoff_toggel": (
                By.XPATH,
                "//input[@name='searchType'][@value='1001']",
            ),
            "3rd_party_pickup_pro_id_input": (
                By.NAME,
                "pickRefNums[1].searchString",
            ),
            "3rd_party_dropoff_pro_id_input": (
                By.NAME,
                "dropRefNums[1].searchString",
            ),
            "3rd_party_pickup_zip_code_input": (By.NAME, "pickGeo"),
            "3rd_party_dropoff_zip_code_input": (By.NAME, "dropGeo"),
        },
        "winland foods 3rd party appt sched - int": {
            "pickup_toggel": (
                By.XPATH,
                "//input[@name='searchType'][@value='1000']",
            ),
            "dropoff_toggel": None,
            "3rd_party_pickup_pro_id_input": (
                By.NAME,
                "pickRefNums[1].searchString",
            ),
            "3rd_party_dropoff_pro_id_input": None,
            "3rd_party_pickup_zip_code_input": (By.NAME, "pickGeo"),
            "3rd_party_dropoff_zip_code_input": None,
        },
    },
}


def fetch_stop_type(warehouse_details: WarehouseDetails) -> list:
    """Fetch stop type from warehouse details.

    Args:
        warehouse_details: Warehouse details

    Returns:
        Stop type
    """
    if warehouse_details.stopType:
        stop_type = [warehouse_details.stopType]
    else:
        stop_type = ["pickup", "dropoff"]
    return stop_type


def close_other_windows(driver: WebDriver, main_window: str) -> None:
    """
    Close all browser windows except the main window.

    Args:
        driver (WebDriver): The Selenium WebDriver instance.
        main_window (str): The window handle of the main window to keep open.

    Returns:
        None

    This function is useful for cleaning up popup or extra windows (e.g., modals, alerts)
    that might have been opened during navigation or form submission.
    """
    try:
        if len(driver.window_handles) > 1:
            for handle in driver.window_handles:
                if handle != main_window:
                    driver.switch_to.window(handle)
                    driver.close()

            driver.switch_to.window(main_window)
    except Exception as e:
        print(f"[close_other_windows] Failed to close extra windows: {e}")


def parse_location(location_text: str) -> WarehouseDetails:
    """Parse warehouse location details from text.

    Args:
        location_text: Raw location text from the website

    Returns:
        Dictionary with parsed location details
    """
    details = WarehouseDetails(
        name="",
        city="",
        state="",
        zipCode="",
        country="",
        website="",
        stopType="",
        openSlots=[],
    )

    try:
        parts = [p.strip() for p in location_text.split("\n") if p.strip()]

        if not parts:
            return details

        # First line is always the name
        details.name = parts[0]

        if len(parts) > 1:
            # Try to match: City, ST ZIP [Country (optional)]
            match = re.match(
                r"^(.*?),\s*([A-Z]{2})\s+(\d{5})(?:\s+([A-Za-z]+))?$", parts[1]
            )
            if match:
                details.city = match.group(1).strip()
                details.state = match.group(2).strip()
                details.zipCode = match.group(3).strip()
                if match.group(4):
                    details.country = match.group(4).strip()
            else:
                logger.warning(
                    f"Could not parse city/state/zip/country from: {json.dumps({'parts[1]': parts[1]})}"
                )

        if len(parts) > 2:
            details.website = parts[2]

    except Exception as e:
        logger.error(f"Error parsing location details: {str(e)}")

    return details


def parse_location_3rd_party(location_text: str) -> WarehouseDetails:
    """Parse warehouse location details from text.

    Args:
        location_text: Raw location text from the website

    Returns:
        Dictionary with parsed location details
    """
    details = WarehouseDetails(
        name="",
        addressLine1="",
        city="",
        state="",
        zipCode="",
        country="",
        website="",
        stopType="",
        openSlots=[],
    )

    try:
        parts = [
            p.strip() for p in location_text.strip().split("\n") if p.strip()
        ]

        if not parts:
            return details

        # Line 1: Name
        details.name = parts[0]

        if len(parts) >= 2:
            details.addressLine1 = parts[1]

        # Line 3: City, State ZIP [Country]
        if len(parts) >= 3:
            address_line = parts[2]
            match = re.match(
                r"^(.*?),\s*([A-Z]{2})\s+(\d{5})(?:\s+([A-Za-z]+))?$",
                address_line,
            )
            if match:
                details.city = match.group(1).strip()
                details.state = match.group(2).strip()
                details.zipCode = match.group(3).strip()
                if match.group(4):
                    details.country = match.group(4).strip()
            else:
                logger.warning(
                    f"Could not parse city/state/zip/country from: {json.dumps({'address_line': address_line})}"
                )

    except Exception as e:
        logger.error(f"Error parsing location details: {str(e)}")

    return details


def nav_to_search(driver: WebDriver) -> None:
    """Navigate to the appointment search page.

    Args:
        driver: WebDriver instance

    Raises:
        Exception: If navigation fails
    """
    current_url = driver.current_url
    logger.debug(f"Current URL: {json.dumps({'current_url': current_url})}")

    appointment_url = (
        f"{LOCATORS['base_url']}{LOCATORS['appointment_search_endpoint']}"
    )

    logger.info("Navigating to appointment search page")
    driver.get(appointment_url)
    wait_for_page_load(driver)
    logger.info("Successfully loaded appointment search page")


def nav_to_3rd_party_appt(driver: WebDriver) -> None:
    """Navigate to the appointment search page.

    Args:
        driver: WebDriver instance

    Raises:
        Exception: If navigation fails
    """
    erd_party_url = (
        f"{LOCATORS['base_url']}{LOCATORS['3rd_party_appointment']}"
    )

    logger.info(f"Navigating to appointment search page")
    driver.get(erd_party_url)
    wait_for_page_load(driver)
    logger.info("Successfully loaded appointment search page")


def fill_search_form(driver: WebDriver, pro_id: str) -> None:
    """Fill out the appointment search form with the specified criteria.

    Args:
        driver: WebDriver instance
        pro_id: The PRO ID to search for in the appointment form

    Raises:
        Exception: If form submission fails
    """
    # Enter PRO number
    logger.debug(f"Entering PRO number: {json.dumps({'pro_id': pro_id})}")
    safe_send_keys(driver, LOCATORS["pro_id_input"], pro_id)

    # Click search button
    logger.info("Clicking search button")
    safe_click(driver, LOCATORS["search_button"])

    # Wait for search results to load
    wait_for_page_load(driver)
    logger.info("Search form submitted successfully")


def switch_popup(driver: WebDriver, main_window: str) -> bool:
    """Switch to a popup window from the main window.

    Args:
        driver: WebDriver instance
        main_window: Handle of the main window

    Returns:
        True if successfully switched to popup, False otherwise
    """
    try:
        # Wait for popup to load
        time.sleep(1)

        if len(driver.window_handles) <= 1:
            logger.warning("No popup window detected")
            return False

        # Switch to the popup window
        for window_handle in driver.window_handles:
            if window_handle != main_window:
                driver.switch_to.window(window_handle)
                logger.info("Switched to appointment scheduling popup window")
                wait_for_page_load(driver)
                return True

        return False
    except Exception as e:
        logger.error(f"Error switching to popup window: {str(e)}")
        return False


def set_date(
    driver: WebDriver,
    date_str: str,
    date_button_locator: tuple = (By.NAME, "nextdatbut"),
) -> bool:
    """Set the date in a datepicker and load available slots.

    Args:
        driver: WebDriver instance
        date_str: Date string in format MM/DD/YYYY
        date_button_locator: Locator for the date button

    Returns:
        True if successful, False otherwise
    """
    try:
        datepicker = driver.find_element(By.CLASS_NAME, "ll_datepicker")

        datepicker.clear()
        safe_send_keys(driver, datepicker, date_str)

        show_open_appointment_button = driver.find_element(
            *date_button_locator
        )
        safe_click(driver, show_open_appointment_button)

        wait_for_page_load(driver)
        time.sleep(1)

        return True
    except Exception as e:
        logger.error(
            f"Error setting datepicker date to {json.dumps({'date_str': date_str})}: {str(e)}"
        )
        return False


def fill_3rd_party_form(
    driver: WebDriver,
    company_name: str,
    pro_id: str,
    zip_code: str,
    stop_type: str = None,
    operation: str = None,
) -> None:
    """Select company and fill out the appointment search form with the specified criteria.

    Args:
        driver: WebDriver instance
        company_name: The company name to select from the list
        pro_id: The PRO ID to search for in the appointment form
        zip_code: The zip code to search for in the appointment form
        stop_type: The stop type to select (default is "None")
        operation: The operation to select (default is "None")

    Raises:
        Exception: If form submission fails
    """
    try:
        if stop_type:
            if stop_type not in ["pickup", "dropoff"]:
                raise ValueError(
                    "stop_type must be either 'pickup' or 'dropoff'"
                )

            company_key = company_name.lower()
            locator_data = COMPANY_WISE_LOCATORS.get(company_key)

            if not locator_data:
                raise ValueError(f"Company {company_name} is not supported")

            if operation:
                operation_data = locator_data.get(operation)

                if not operation_data:
                    raise ValueError(
                        f"Company {company_name} does not support {operation}."
                    )

                if not operation_data.get(
                    f"3rd_party_{stop_type}_pro_id_input"
                ):
                    raise ValueError(
                        f"Company {company_name} does not support {operation} operation for {stop_type} appointments"
                    )
            else:
                if not locator_data.get(f"3rd_party_{stop_type}_pro_id_input"):
                    raise ValueError(
                        f"Company {company_name} does not support {stop_type} appointments"
                    )

        company_xpath = f"//label[contains(text(), '{company_name.upper()}')]"

        company_label = driver.find_element(By.XPATH, company_xpath)
        logger.info(
            f"Found company: {json.dumps({'company_name': company_name})}"
        )

        radio_id = company_label.get_attribute("for")

        radio_button = driver.find_element(By.ID, radio_id)
        safe_click(driver, radio_button)

        wait_for_page_load(driver)

        company_locator = COMPANY_WISE_LOCATORS[company_name.lower()]

        if operation:
            operation_xpath = (
                f"//label[contains(text(), '{operation.upper()}')]"
            )
            operation_label = driver.find_element(By.XPATH, operation_xpath)
            operation_radio_id = operation_label.get_attribute("for")
            operation_radio_button = driver.find_element(
                By.ID, operation_radio_id
            )
            safe_click(driver, operation_radio_button)
            wait_for_page_load(driver)

            if operation.lower() in company_locator:
                company_locator = company_locator[operation.lower()]
            elif "pickup_toggel" not in company_locator:
                raise ValueError(
                    f"Company {company_name} does not support {operation} operation"
                )

        if not stop_type:
            if company_locator["pickup_toggel"]:
                stop_type = "pickup"
            else:
                stop_type = "dropoff"

        if (
            f"{stop_type}_toggel" not in company_locator
            or not company_locator[f"{stop_type}_toggel"]
        ):
            raise ValueError(
                f"Company {company_name} does not support {stop_type} appointments"
            )

        if stop_type == "pickup":
            pickup_toggle = company_locator["pickup_toggel"]
            WebDriverWait(driver, 10).until(
                EC.visibility_of_element_located(pickup_toggle)
            )
            safe_click(driver, pickup_toggle)
        else:
            dropoff_toggle = company_locator["dropoff_toggel"]
            WebDriverWait(driver, 10).until(
                EC.visibility_of_element_located(dropoff_toggle)
            )
            safe_click(driver, dropoff_toggle)

        safe_send_keys(
            driver,
            company_locator[f"3rd_party_{stop_type}_pro_id_input"],
            pro_id,
        )

        if company_locator[f"3rd_party_{stop_type}_zip_code_input"]:
            safe_send_keys(
                driver,
                company_locator[f"3rd_party_{stop_type}_zip_code_input"],
                zip_code,
            )

        safe_click(driver, LOCATORS["search_button"])

        wait_for_page_load(driver)
        logger.info("Search form submitted successfully")

    except Exception as e:
        logger.error(
            f"Error in company selection and form submission: {str(e)}"
        )
        raise Exception(f"Failed to select company and submit form: {str(e)}")


def _match_warehouse(
    row_details: WarehouseDetails, target_details: Optional[WarehouseDetails]
) -> bool:
    """Match warehouse details from row with target warehouse details.

    Args:
        row_details: Warehouse details parsed from the row
        target_details: Target warehouse details from the request

    Returns:
        True if the warehouses match, False otherwise
    """
    if not target_details:
        return True

    exclude_fields = {"open_slots", "website"}
    matches = []

    for field_name in target_details.model_fields:
        if field_name in exclude_fields:
            continue

        target_value = getattr(target_details, field_name, None)
        row_value = getattr(row_details, field_name, None)

        if target_value and row_value:
            matches.append(target_value.lower() in row_value.lower())

    return not matches or all(matches)


def _validate_slots(
    request: E2openGetOpenSlotsRequest,
) -> E2openOpenSlotsValidationResult:
    """Validate the open slots request parameters.

    Args:
        request: The open slots request

    Returns:
        Validation result with parsed dates if successful
    """
    try:
        start_date = datetime.strptime(request.startDate, "%Y-%m-%d")
        end_date = datetime.strptime(request.endDate, "%Y-%m-%d")

        return E2openOpenSlotsValidationResult(
            success=True, startDate=start_date, endDate=end_date
        )
    except ValueError as e:
        return E2openOpenSlotsValidationResult(
            success=False, message=f"Invalid date format: {str(e)}"
        )
    except Exception as e:
        return E2openOpenSlotsValidationResult(
            success=False, message=f"Invalid request parameters: {str(e)}"
        )


def _find_matches(
    driver: WebDriver,
    target_warehouse: Optional[WarehouseDetails],
    pro_id: str,
    request_type: Optional[str] = None,
) -> E2openMatchingRowsResult:
    """Find the appropriate appointment link based on request type and
    warehouse details.

    Args:
        driver: WebDriver instance
        request_type: Type of request (pickup or dropoff)
        target_warehouse: Target warehouse details
        pro_id: PRO ID

    Returns:
        List of matching rows and respective warehouse details.
    """

    stop_type_mapping = {"pick": "pickup", "drop": "dropoff"}

    if not request_type or request_type.lower() in ["pickup", "dropoff"]:
        if request_type:
            request_type_opt = {"pickup": "Pick", "dropoff": "Drop"}.get(
                request_type.lower()
            )
            rows = driver.find_elements(
                By.XPATH, f"//tr[.//strong[text()='{request_type_opt}']]"
            )
        else:
            request_type = "pickup/dropoff"
            rows = driver.find_elements(
                By.XPATH, "//tr[.//strong[text()='Pick' or text()='Drop']]"
            )

        if not rows:
            return E2openMatchingRowsResult(
                success=False,
                message=(
                    f"No {request_type.lower()} locations found "
                    f"for PRO ID {pro_id}"
                ),
                errors=[
                    f"No {request_type.lower()} locations found for PRO ID {pro_id}"
                ],
            )

        matching_rows: List[E2openMatchingRow] = []

        for row in rows:
            stop_type = row.find_element(By.XPATH, "./td[3]").text
            if stop_type == "Pick":
                location_element = row.find_element(By.XPATH, "./td[4]")
                location_text = location_element.text
            else:
                stop_type = row.find_element(By.XPATH, "./td[2]").text
                location_element = row.find_element(By.XPATH, "./td[3]")
                location_text = location_element.text

            row_warehouse_details = parse_location(location_text)
            row_warehouse_details.stopType = stop_type_mapping.get(
                stop_type.lower()
            )

            if _match_warehouse(row_warehouse_details, target_warehouse):
                matching_rows.append(
                    E2openMatchingRow(
                        row=row, warehouseDetails=row_warehouse_details
                    )
                )

        if not matching_rows:
            # If no match found, return no appointments found
            warehouse_name = (
                target_warehouse.name
                if target_warehouse
                else "specified location"
            )
            return E2openMatchingRowsResult(
                success=False,
                message=f"No appointments found for PRO ID {pro_id} at {warehouse_name}",
                errors=[
                    f"No matching {request_type.lower()} location found for the specified warehouse details"
                ],
            )

        return E2openMatchingRowsResult(
            success=True,
            message="Successfully found matching rows",
            matchingRows=matching_rows,
        )

    return E2openMatchingRowsResult(
        success=False,
        message=f"No open slots available for {request_type}",
        errors=[f"No open slots found for {request_type}"],
    )


def _collect_slots(
    driver: WebDriver,
    start_date: datetime,
    end_date: datetime,
    warehouse_details: WarehouseDetails,
    request_type: str = "normal",
) -> WarehouseDetails:
    """Collect open appointment slots for the given date range.

    Args:
        driver: WebDriver instance
        request_type: Type of request (normal or 3rd_party)
        start_date: Start date for slot search
        end_date: End date for slot search
        warehouse_details: Details of the warehouse

    Returns:
        Dictionary with warehouse details and open slots
    """
    slots = []
    current_date = start_date

    while current_date <= end_date:
        current_date_str = current_date.strftime("%m/%d/%Y")

        if request_type == "normal":
            set_date(driver, current_date_str)
        else:
            set_date(
                driver,
                current_date_str,
                date_button_locator=(By.ID, "test-show-open-appts-button"),
            )

        driver.implicitly_wait(0)
        slot_elements = driver.find_elements(
            By.CSS_SELECTOR, "input[name='startSlotNum']"
        )
        driver.implicitly_wait(10)

        for slot_element in slot_elements:
            # parent_td = slot_element.find_element(By.XPATH, "./ancestor::td")

            label_element = slot_element.find_element(
                By.XPATH, "./parent::label"
            )
            time_text = label_element.text.strip()

            # dock_id_element = parent_td.find_element(By.CSS_SELECTOR, "input[name^='dockID']")
            # dock_id = dock_id_element.get_attribute("value")

            # slot_value = slot_element.get_attribute("value")

            slots.append(
                AppointmentSlot(
                    scheduledTime=f"{current_date.strftime('%Y-%m-%d')}T{time_text}:00",
                    duration=60,
                )
            )

        current_date += timedelta(days=1)

    warehouse_details.openSlots = slots
    return warehouse_details


def _validate_make_appointment(
    appointment: E2openAppointmentData,
) -> E2openAppointmentValidationResult:
    """Validate the make appointment request parameters.

    Args:
        appointment: The make appointment request

    Returns:
        Dictionary with validation results
    """
    try:
        if appointment.requestType == "3rd_party" and not appointment.scac:
            raise ValueError(
                "SCAC is required for 3rd party appointment requests"
            )

        E2openAppointmentRequestValidator(
            proId=appointment.proId,
            appointmentTime=appointment.appointmentTime,
            warehouse=appointment.warehouse,
        )

        E2openWarehouseValidator(stopType=appointment.warehouse.stopType)

        return E2openAppointmentValidationResult(success=True)
    except ValueError as e:
        return E2openAppointmentValidationResult(success=False, message=str(e))
    except Exception as e:
        return E2openAppointmentValidationResult(
            success=False,
            message=f"Invalid request parameters: {str(e)}",
        )


def handle_alerts(driver, window=None):
    """Handle any alerts that might be present.

    Args:
        driver: WebDriver instance
        window: Handle of the popup window if applicable

    Returns:
        True if alert was handled, False if no alert was present
    """
    try:
        alert = driver.switch_to.alert
        alert_text = alert.text.split("\n")[0]
        logger.info(
            f"Handling alert: {json.dumps({'alert_text': alert_text})}"
        )
        alert.accept()
        if window:
            driver.switch_to.window(window)
        return True, alert_text
    except Exception:
        return False, ""
