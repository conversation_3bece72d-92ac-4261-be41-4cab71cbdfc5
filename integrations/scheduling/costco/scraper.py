"""Costco Selenium scheduling implementation."""

from typing import Optional, Type, Tuple
import functools
import json

from selenium.common.exceptions import TimeoutException
from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Web<PERSON>riverWait

from cache import load_cookies, save_cookies
from config import PLATFORM_URLS
from session import add_cookies, with_driver
from utils.logging import logger
from utils.selenium import (
    is_element_present,
    safe_click,
    safe_send_keys,
    wait_for_element,
    wait_for_page_load,
)

from models.base import BaseResponse


from integrations.scheduling.models import (
    Credentials,
    Appointment,
    LoginResponse,
    GetWarehouseResponse,
    GetOpenSlotsResponse,
    GetLoadTypesResponse,
    CancelAppointmentResponse,
    GetAppointmentResponse,
    MakeAppointmentResponse,
    UpdateAppointmentResponse,
)


from integrations.scheduling.costco.models import (
    CostcoLoginRequest,
    CostcoGetWarehouseRequest,
    CostcoGetOpenSlotsRequest,
    CostcoGetLoadTypesRequest,
    CostcoCancelAppointmentRequest,
    CostcoGetAppointmentRequest,
    CostcoMakeAppointmentRequest,
    CostcoUpdateAppointmentRequest,
)

# Costco element locators
LOCATORS = {
    # Login indicators
    "username_field": (By.ID, "P1001_USERNAME"),
    "password_field": (By.ID, "P1001_PASSWORD"),
    "login_button": (By.ID, "P1001_LOGIN_BUTTON"),
    "logged_in_indicator": (By.ID, "<TBD>"),
}


def parse_appointment_row(row_element) -> Optional[Appointment]:
    """Parse an appointment row element into an Appointment object.

    Args:
        row_element: Table row WebElement

    Returns:
        Appointment object or None if parsing fails
    """
    # Implementation needed here
    pass


def is_logged_in(driver: WebDriver) -> bool:
    """Check if we're currently logged in.

    Args:
        driver: WebDriver instance

    Returns:
        True if logged in, False otherwise
    """
    try:
        return is_element_present(
            driver, LOCATORS["logged_in_indicator"], timeout=2
        )
    except:
        return False


def perform_login(
    driver: WebDriver, credentials: Credentials
) -> Tuple[bool, Optional[str]]:
    """Perform actual login operation.

    Args:
        driver: WebDriver instance
        credentials: User credentials

    Returns:
        True if login successful, False otherwise
    """
    try:
        logger.debug("Starting Costco login process")

        wait_for_element(driver, LOCATORS["username_field"])
        safe_send_keys(
            driver, LOCATORS["username_field"], credentials.username
        )
        safe_send_keys(
            driver, LOCATORS["password_field"], credentials.password
        )

        safe_click(driver, LOCATORS["login_button"])
        wait_for_page_load(driver)

        try:
            alert = WebDriverWait(driver, 5).until(EC.alert_is_present())
            error_msg = alert.text
            alert.accept()
            logger.error(
                f"Costco Login failed: {json.dumps({'error_msg': error_msg})}"
            )
            return False, error_msg
        except TimeoutException:
            pass

        logger.debug(
            f"Current Costco URL: {json.dumps({'driver.current_url': driver.current_url})}"
        )
        logger.debug(
            f"Costco Page Source:\n{json.dumps({'driver.page_source': driver.page_source})}\n"
        )

        return is_logged_in(driver), None

    except Exception as e:
        logger.error(f"Costco Login failed with error: {str(e)}")
        return False, str(e)


def ensure_logged_in(
    driver: WebDriver, credentials: Credentials
) -> Tuple[bool, Optional[str]]:
    """Ensure user is logged in, using cached cookies if possible.

    Args:
        driver: WebDriver instance
        credentials: User credentials

    Returns:
        True if logged in successfully, False otherwise
    """
    cookies = load_cookies("Costco", credentials.username)
    if cookies:
        logger.info(
            f"Found saved cookies for user {json.dumps({'credentials.username': credentials.username})}"
        )
        try:
            driver.get(PLATFORM_URLS["costco"])

            add_cookies(driver, cookies)

            driver.get(PLATFORM_URLS["costco"])

            if is_logged_in(driver):
                logger.info("Successfully logged in using cached cookies")
                return True, None
            else:
                logger.info("Cached cookies expired or invalid")
        except Exception as e:
            logger.warning(f"Error using cached cookies: {str(e)}")

    logger.info(
        f"Attempting fresh login for user {json.dumps({'credentials.username': credentials.username})}"
    )

    try:
        driver.get(PLATFORM_URLS["costco"])

        if perform_login(driver, credentials):
            logger.info(
                f"Login successful, caching cookies for {json.dumps({'credentials.username': credentials.username})}"
            )
            new_cookies = driver.get_cookies()
            save_cookies("Costco", credentials.username, new_cookies)
            return True, None
        else:
            logger.error("Login failed")
            return False, None

    except Exception as e:
        logger.error(f"Login process failed with error: {str(e)}")
        return False, str(e)


def requires_login(response_cls: Type[BaseResponse]):
    """Decorator that ensures user is logged in before executing the handler."""

    def decorator(handler):
        @functools.wraps(handler)
        def wrapper(request, driver, *args, **kwargs):
            success, error_msg = ensure_logged_in(driver, request.credentials)
            if not success:
                return response_cls(
                    success=False,
                    message="Authentication failed",
                    errors=[
                        (
                            error_msg
                            if error_msg
                            else "Failed to log in with provided credentials"
                        )
                    ],
                )
            return handler(request, driver, *args, **kwargs)

        return wrapper

    return decorator


@with_driver
def login(request: CostcoLoginRequest, driver: WebDriver) -> LoginResponse:
    """Log in to Costco.

    Args:
        request: Login request
        driver: WebDriver instance provided by decorator

    Returns:
        Login response
    """
    try:
        success, error_msg = perform_login(driver, request.credentials)

        if success:
            return LoginResponse(
                success=True,
                message="Successfully logged in",
                userDetails={"username": request.credentials.username},
            )
        else:
            return LoginResponse(
                success=False,
                message="Login failed",
                errors=[error_msg if error_msg else "Unknown login error"],
            )

    except Exception as e:
        return LoginResponse(
            success=False,
            message=f"Error during login: {str(e)}",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetLoadTypesResponse)
def get_load_types(
    request: CostcoGetLoadTypesRequest, driver: WebDriver
) -> GetLoadTypesResponse:
    """Get load types from Costco.

    Args:
        request: Get load types request
        driver: WebDriver instance provided by decorator

    Returns:
        Get load types response
    """
    try:
        return GetLoadTypesResponse(
            success=False,
            message="Not implemented yet",
            errors=["Load types fetching not implemented"],
        )
    except Exception as e:
        return GetLoadTypesResponse(
            success=False,
            message="Failed to fetch load types",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetOpenSlotsResponse)
def get_open_slots(
    request: CostcoGetOpenSlotsRequest, driver: WebDriver
) -> GetOpenSlotsResponse:
    """Get open appointment slots from Costco.

    Args:
        request: Get open slots request
        driver: WebDriver instance provided by decorator

    Returns:
        Get open slots response
    """
    try:
        return GetOpenSlotsResponse(
            success=False,
            message="Not implemented yet",
            errors=["Open slots fetching not implemented"],
        )
    except Exception as e:
        return GetOpenSlotsResponse(
            success=False,
            message="Failed to fetch open slots",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetWarehouseResponse)
def get_warehouse(
    request: CostcoGetWarehouseRequest, driver: WebDriver
) -> GetWarehouseResponse:
    """Get warehouse information from Costco.

    Args:
        request: Get warehouse request
        driver: WebDriver instance provided by decorator

    Returns:
        Get warehouse response
    """
    try:
        return GetWarehouseResponse(
            success=False,
            message="Not implemented yet",
            errors=["Warehouse fetching not implemented"],
        )
    except Exception as e:
        return GetWarehouseResponse(
            success=False, message="Failed to fetch warehouse", errors=[str(e)]
        )


@with_driver
@requires_login(CancelAppointmentResponse)
def cancel_appointment(
    request: CostcoCancelAppointmentRequest, driver: WebDriver
) -> CancelAppointmentResponse:
    """Cancel an appointment in Costco.

    Args:
        request: Cancel appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Cancel appointment response
    """
    try:
        return CancelAppointmentResponse(
            success=False,
            message="Not implemented yet",
            errors=["Appointment canceling not implemented"],
        )
    except Exception as e:
        return CancelAppointmentResponse(
            success=False,
            message="Failed to cancel appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetAppointmentResponse)
def get_appointment(
    request: CostcoGetAppointmentRequest, driver: WebDriver
) -> GetAppointmentResponse:
    """Get an appointment from Costco.

    Args:
        request: Get appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Get appointment response
    """
    try:
        return GetAppointmentResponse(
            success=False,
            message="Not implemented yet",
            errors=["Appointment fetching not implemented"],
        )
    except Exception as e:
        return GetAppointmentResponse(
            success=False, message="Failed to get appointment", errors=[str(e)]
        )


@with_driver
@requires_login(MakeAppointmentResponse)
def make_appointment(
    request: CostcoMakeAppointmentRequest, driver: WebDriver
) -> MakeAppointmentResponse:
    """Create an appointment in Costco.

    Args:
        request: Make appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Make appointment response
    """
    try:
        return MakeAppointmentResponse(
            success=False,
            message="Not implemented yet",
            errors=["Appointment making not implemented"],
        )
    except Exception as e:
        return MakeAppointmentResponse(
            success=False,
            message="Failed to make appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(UpdateAppointmentResponse)
def update_appointment(
    request: CostcoUpdateAppointmentRequest, driver: WebDriver
) -> UpdateAppointmentResponse:
    """Update an appointment in Costco.

    Args:
        request: Update appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Update appointment response
    """
    try:
        return UpdateAppointmentResponse(
            success=False,
            message="Not implemented yet",
            errors=["Appointment updating not implemented"],
        )
    except Exception as e:
        return UpdateAppointmentResponse(
            success=False,
            message="Failed to update appointment",
            errors=[str(e)],
        )
