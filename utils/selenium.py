"""Selenium helper functions."""

import json
import time
from typing import Callable, Optional, Union

from selenium.common.exceptions import (
    ElementClickInterceptedException,
    ElementNotInteractableException,
    StaleElementReferenceException,
    TimeoutException,
)
from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.remote.webelement import WebElement
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from config import DEFAULT_TIMEOUT
from utils.logging import logger


def wait_for_element(
    driver: WebDriver,
    locator: tuple,
    timeout: int = DEFAULT_TIMEOUT,
    condition: Callable = EC.presence_of_element_located,
) -> Optional[WebElement]:
    """Wait for an element to be present/visible/clickable.

    Args:
        driver: WebDriver instance
        locator: Element locator (By, value)
        timeout: Maximum time to wait in seconds
        condition: Expected condition to wait for

    Returns:
        The WebElement if found, None otherwise
    """
    try:
        return WebDriverWait(driver, timeout).until(condition(locator))
    except TimeoutException:
        logger.warning(
            f"Timed out waiting for element: {json.dumps({'locator': locator})}"
        )
        return None


def safe_click(
    driver: WebDriver,
    element_or_locator: Union[WebElement, tuple],
    timeout: int = DEFAULT_TIMEOUT,
    retry_count: int = 3,
) -> bool:
    """Safely click an element with retries.

    Args:
        driver: WebDriver instance
        element_or_locator: WebElement or locator tuple
        timeout: Maximum time to wait in seconds
        retry_count: Number of times to retry

    Returns:
        True if successful, False otherwise
    """
    element = (
        wait_for_element(
            driver, element_or_locator, timeout, EC.element_to_be_clickable
        )
        if isinstance(element_or_locator, tuple)
        else element_or_locator
    )

    if not element:
        return False

    for i in range(retry_count):
        try:
            element.click()
            return True
        except (
            StaleElementReferenceException,
            ElementClickInterceptedException,
        ) as e:
            if i == retry_count - 1:
                logger.error(
                    f"Failed to click element after {retry_count} retries: {str(e)}"
                )
                return False

            logger.warning(
                f"Click failed, retrying ({i+1}/{retry_count}): {str(e)}"
            )
            time.sleep(0.5)

    return False


def safe_send_keys(
    driver: WebDriver,
    element_or_locator: Union[WebElement, tuple],
    text: str,
    timeout: int = DEFAULT_TIMEOUT,
    clear_first: bool = True,
) -> bool:
    """Safely send keys to an element.

    Args:
        driver: WebDriver instance
        element_or_locator: WebElement or locator tuple
        text: Text to send
        timeout: Maximum time to wait in seconds
        clear_first: Whether to clear the field first

    Returns:
        True if successful, False otherwise
    """
    element = (
        wait_for_element(
            driver,
            element_or_locator,
            timeout,
            EC.visibility_of_element_located,
        )
        if isinstance(element_or_locator, tuple)
        else element_or_locator
    )

    if not element:
        return False

    try:
        if clear_first:
            element.clear()
        element.send_keys(text)
        return True
    except (
        StaleElementReferenceException,
        ElementNotInteractableException,
    ) as e:
        logger.error(f"Failed to send keys: {str(e)}")
        return False


def is_element_present(
    driver: WebDriver, locator: tuple, timeout: int = 2
) -> bool:
    """Check if an element is present on the page.

    Args:
        driver: WebDriver instance
        locator: Element locator (By, value)
        timeout: Maximum time to wait in seconds

    Returns:
        True if the element is present, False otherwise
    """
    return wait_for_element(driver, locator, timeout) is not None


def get_element_text(
    driver: WebDriver,
    locator: tuple,
    timeout: int = DEFAULT_TIMEOUT,
) -> Optional[str]:
    """Get the text of an element.

    Args:
        driver: WebDriver instance
        locator: Element locator (By, value)
        timeout: Maximum time to wait in seconds

    Returns:
        The element text, or None if not found
    """
    element = wait_for_element(
        driver, locator, timeout, EC.visibility_of_element_located
    )
    if not element:
        return None

    try:
        return element.text
    except Exception as e:
        logger.error(f"Error getting element text: {str(e)}")
        return None


def wait_for_page_load(driver: WebDriver, timeout: int = 30) -> None:
    """Wait for the page to finish loading.

    Args:
        driver: WebDriver instance
        timeout: Maximum time to wait in seconds
    """
    try:
        WebDriverWait(driver, timeout).until(
            lambda d: d.execute_script("return document.readyState")
            == "complete"
        )
    except TimeoutException:
        logger.warning("Timed out waiting for page to load")
