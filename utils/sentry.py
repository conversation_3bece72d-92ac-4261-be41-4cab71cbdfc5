"""Sentry initialization and configuration."""

import json

import sentry_sdk
from sentry_sdk.integrations.starlette import StarletteIntegration
from sentry_sdk.integrations.fastapi import FastApiIntegration

from config import APP_ENV, SENTRY_DSN
from utils.logging import logger


def init_sentry() -> None:
    """Initialize Sentry SDK."""

    if not SENTRY_DSN:
        if APP_ENV != "development":
            logger.warning("Sentry not initialized without DSN")
        return

    sentry_sdk.init(
        dsn=SENTRY_DSN,
        environment=APP_ENV,
        traces_sample_rate=1.0,
        profiles_sample_rate=1.0,
        integrations=[
            StarletteIntegration(
                transaction_style="endpoint",
                failed_request_status_codes={403, *range(500, 599)},
                http_methods_to_capture=("GET",),
            ),
            FastApiIntegration(
                transaction_style="endpoint",
                failed_request_status_codes={403, *range(500, 599)},
                http_methods_to_capture=("GET",),
            ),
        ],
    )

    logger.info(
        f"Sentry initialized in {json.dumps({'APP_ENV': APP_ENV})} with DSN: {json.dumps({'SENTRY_DSN': SENTRY_DSN})}"
    )
