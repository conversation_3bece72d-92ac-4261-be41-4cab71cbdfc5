"""S3 uploader service for HTML pages."""

import json
import os

import boto3
from botocore.exceptions import ClientError

from config import BASE_LOCAL_DIR, S3_BUCKET_NAME
from utils.logging import logger


class S3Uploader:
    """Service for uploading any file to S3."""

    def __init__(
        self,
        base_local_dir: str = BASE_LOCAL_DIR,
        bucket_name: str = S3_BUCKET_NAME,
    ):
        self.bucket_name = bucket_name
        self.base_local_dir = os.path.abspath(base_local_dir)
        self.s3_client = boto3.client("s3")

    def upload_file(self, file_path: str) -> str:
        """Upload a file to S3 maintaining relative path structure."""
        try:
            abs_file_path = os.path.abspath(file_path)
            if not abs_file_path.startswith(self.base_local_dir):
                raise ValueError(
                    "File is outside the designated staging directory."
                )

            s3_client = self.s3_client

            # Relative path from staging dir becomes the S3 key
            relative_path = os.path.relpath(abs_file_path, self.base_local_dir)
            s3_key = relative_path.replace(
                os.sep, "/"
            )  # normalize path for S3

            s3_client.upload_file(
                abs_file_path,
                self.bucket_name,
                s3_key,
                ExtraArgs={"ContentType": self._guess_mime_type(file_path)},
            )

            s3_url = f"https://{self.bucket_name}.s3.amazonaws.com/{s3_key}"
            logger.info(
                f"Successfully uploaded file to {json.dumps({'s3_url': s3_url})}"
            )

            # Clean up only after confirmed success
            if os.path.exists(file_path):
                os.remove(file_path)

            return s3_url

        except ClientError as e:
            logger.error(f"S3 upload error: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Error uploading file to S3: {str(e)}")
            raise

    def _guess_mime_type(self, file_path: str) -> str:
        """Rudimentary mime type inference based on file extension."""
        ext = os.path.splitext(file_path)[1].lower()
        return {
            ".html": "text/html",
            ".css": "text/css",
            ".js": "application/javascript",
            ".json": "application/json",
            ".txt": "text/plain",
        }.get(ext, "application/octet-stream")
